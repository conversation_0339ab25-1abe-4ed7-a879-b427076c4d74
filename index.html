<!DOCTYPE html>
<html lang="it">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description"
        content="Domina Amazon KDP con KDP GENIUS, il primo tool strategico che ti guida passo dopo passo nel self publishing, dalla visione alla pubblicazione.">
    <meta name="keywords"
        content="KDP GENIUS, self publishing, Amazon KDP, tool self publishing, strategia editoriale, pubblicare su amazon">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <title>KDP GENIUS - Il Tuo Stratega Editoriale per il Self Publishing</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;600&display=swap');

        :root {
            --notion-bg: #ffffff;
            --notion-text-primary: #1d1d1f;
            --notion-text-secondary: #86868b;
            --notion-accent-subtle: #f5f5f7;
            --notion-border: #d2d2d7;
            --notion-cta-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --notion-cta-text: #ffffff;
            --notion-cta-hover-bg: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            --primary-blue-muted: #0071e3;

            --border-radius-subtle: 12px;
            --box-shadow-subtle: 0 4px 8px rgba(0, 0, 0, 0.04);
            --box-shadow-none: none;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.47059;
            color: var(--notion-text-primary);
            background-color: var(--notion-bg);
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-weight: 400;
            letter-spacing: -0.022em;
        }

        .container {
            max-width: 1024px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Navigation Bar - Silicon Valley Style */
        .site-nav {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 1001;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 60px;
            position: relative;
        }

        .site-logo img {
            height: 60px;
            display: block;
        }

        .site-logo {
            text-decoration: none;
            display: flex;
            align-items: center;
            margin-right: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            font-size: 2em;
            font-weight: 700;
            color: var(--notion-text-primary);
            letter-spacing: -0.02em;
        }

        .nav-links {
            display: flex;
            align-items: center;
            /* margin-left: auto; This will be handled by media query for desktop */
        }

        .nav-links .main-nav-links {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }

        .nav-links a {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            text-decoration: none;
            color: var(--notion-text-secondary);
            margin-left: 32px;
            font-weight: 500;
            font-size: 1.0625em;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
            padding: 12px 8px;
            line-height: 1.4;
            display: flex;
            align-items: center;
            letter-spacing: -0.01em;
        }

        .nav-links .main-nav-links a:first-child {
            margin-left: 0;
        }

        .nav-links a:hover {
            color: var(--notion-text-primary);
            transform: translateY(-1px);
        }

        .nav-links .nav-cta-button {
            background: var(--notion-cta-bg);
            color: var(--notion-cta-text);
            padding: 8px 12px;
            /* Adjusted padding */
            border-radius: var(--border-radius-subtle);
            font-weight: 500;
            font-size: 0.82em;
            /* Adjusted font size */
            margin-left: 12px;
            /* Adjusted margin */
            white-space: nowrap;
            line-height: 1.4;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
        }

        .nav-links .nav-cta-button:hover {
            background: var(--notion-cta-hover-bg);
            color: var(--notion-cta-text);
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.3);
        }

        .nav-links .nav-login-link {
            white-space: nowrap;
        }

        /* Mobile Navigation Styles */
        #mobile-nav-checkbox {
            display: none;
        }

        .mobile-nav-toggle-label {
            display: none;
            cursor: pointer;
            padding: 10px;
            margin-left: auto;
            /* Push toggle to the right */
            z-index: 1002;
            position: relative;
        }

        .hamburger-icon {
            display: block;
            width: 22px;
            height: 2px;
            background-color: var(--notion-text-primary);
            position: relative;
            transition: background-color 0.1s 0.2s ease-in-out;
        }

        .hamburger-icon::before,
        .hamburger-icon::after {
            content: '';
            position: absolute;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--notion-text-primary);
            transition: transform 0.2s ease-in-out, top 0.2s ease-in-out 0.2s;
        }

        .hamburger-icon::before {
            top: -6px;
        }

        .hamburger-icon::after {
            top: 6px;
        }

        #mobile-nav-checkbox:checked~.mobile-nav-toggle-label .hamburger-icon {
            background-color: transparent;
        }

        #mobile-nav-checkbox:checked~.mobile-nav-toggle-label .hamburger-icon::before {
            transform: rotate(45deg);
            top: 0;
            transition: top 0.2s ease-in-out, transform 0.2s ease-in-out 0.2s;
        }

        #mobile-nav-checkbox:checked~.mobile-nav-toggle-label .hamburger-icon::after {
            transform: rotate(-45deg);
            top: 0;
            transition: top 0.2s ease-in-out, transform 0.2s ease-in-out 0.2s;
        }

        /* Hero Section */
        header.hero {
            background-color: var(--notion-bg);
            color: var(--notion-text-primary);
            padding: 80px 0;
            /* Reverted to a slightly smaller padding than 100px */
            border-bottom: 1px solid var(--notion-border);
            margin-top: 0;
        }

        .hero-container {
            display: flex;
            align-items: center;
            gap: 40px;
            /* Reverted to original gap */
        }

        .hero-text-content {
            flex: 1;
            text-align: left;
        }

        .hero-image-placeholder {
            flex: 1;
            height: 350px;
            /* Adjusted height */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hero-image-placeholder img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            display: block;
        }

        header.hero h1 {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            font-size: 3.5em;
            font-weight: 600;
            margin-bottom: 0.5em;
            line-height: 1.05;
            color: var(--notion-text-primary);
            letter-spacing: -0.015em;
        }

        header.hero h2 {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            font-size: 1.375em;
            font-weight: 400;
            max-width: none;
            margin: 0 0 40px 0;
            color: var(--notion-text-secondary);
            line-height: 1.381;
            letter-spacing: -0.01em;
        }

        .hero-cta-button {
            margin-top: 10px;
        }

        /* General Section Styling */
        section {
            padding: 70px 0;
            border-bottom: 1px solid var(--notion-accent-subtle);
        }

        section:last-of-type {
            border-bottom: none;
        }

        .section-title {
            text-align: center;
            font-family: 'Montserrat', sans-serif;
            font-size: 2em;
            font-weight: 600;
            margin-bottom: 50px;
            color: var(--notion-text-primary);
        }

        /* Challenges Section */
        .challenges-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .challenge-card {
            background-color: var(--notion-accent-subtle);
            padding: 25px;
            border-radius: var(--border-radius-subtle);
            border: 1px solid var(--notion-border);
        }

        .challenge-card h3 {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.15em;
            font-weight: 600;
            color: var(--notion-text-primary);
            margin-top: 0;
            margin-bottom: 10px;
        }

        .challenge-card p {
            font-size: 0.95em;
            color: var(--notion-text-secondary);
            line-height: 1.6;
            margin-bottom: 0;
        }

        /* Steps Grid */
        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
        }

        .step-card {
            background: var(--notion-bg);
            padding: 25px;
            border-radius: var(--border-radius-subtle);
            border: 1px solid var(--notion-border);
            display: flex;
            flex-direction: column;
            transition: border-color 0.2s ease;
        }

        .step-card:hover {
            border-color: var(--primary-blue-muted);
            transform: none;
            box-shadow: var(--box-shadow-none);
        }

        .step-card h3 {
            font-family: 'Montserrat', sans-serif;
            color: var(--notion-text-primary);
            margin-top: 0;
            font-size: 1.25em;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .step-card p {
            font-size: 0.9em;
            color: var(--notion-text-secondary);
            flex-grow: 1;
            margin-bottom: 15px;
        }

        .step-card .benefit {
            font-style: normal;
            color: var(--notion-text-secondary);
            padding-top: 15px;
            margin-top: auto;
            border-top: 1px solid var(--notion-accent-subtle);
            font-size: 0.85em;
        }

        .step-card .benefit strong {
            font-weight: 600;
            color: var(--notion-text-primary);
        }

        .new-badge {
            background-color: var(--notion-accent-subtle);
            color: var(--notion-text-secondary);
            font-size: 0.7em;
            padding: 3px 8px;
            border-radius: var(--border-radius-subtle);
            font-weight: 500;
            display: inline-block;
            margin-left: 8px;
            vertical-align: middle;
            border: 1px solid var(--notion-border);
        }

        .usp-list {
            list-style: none;
            padding-left: 0;
        }

        .usp-list li {
            padding-left: 25px;
            margin-bottom: 15px;
            font-size: 1em;
            position: relative;
            color: var(--notion-text-primary);
        }

        .usp-list li::before {
            content: '•';
            color: var(--notion-text-secondary);
            position: absolute;
            left: 5px;
            top: 1px;
            font-size: 1.2em;
        }

        .cta-button {
            display: inline-block;
            background: var(--notion-cta-bg);
            color: var(--notion-cta-text);
            padding: 16px 32px;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            font-size: 1.0625em;
            font-weight: 500;
            text-decoration: none;
            border-radius: var(--border-radius-subtle);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
            text-align: center;
            border: none;
            letter-spacing: -0.01em;
            position: relative;
            overflow: hidden;
        }

        .cta-button:hover {
            background: var(--notion-cta-hover-bg);
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.35);
        }

        .cta-button:active {
            transform: translateY(0px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.25);
        }

        .cta-section {
            text-align: center;
            background: var(--notion-accent-subtle);
            color: var(--notion-text-primary);
            padding-top: 70px;
            padding-bottom: 70px;
        }

        .cta-section h2 {
            font-size: 1.8em;
            font-weight: 600;
            color: var(--notion-text-primary);
        }

        .cta-section p {
            color: var(--notion-text-secondary);
            font-size: 1em;
            max-width: 650px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .placeholder {
            background-color: var(--notion-bg);
            border: 1px dashed var(--notion-border);
            padding: 20px;
            border-radius: var(--border-radius-subtle);
            text-align: center;
            color: var(--notion-text-secondary);
            margin-top: 20px;
            font-size: 0.9em;
        }

        .placeholder p {
            font-size: inherit;
            color: inherit;
        }

        .cta-section .placeholder {
            background-color: var(--notion-bg);
        }

        .faq dt {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            font-size: 1.15em;
            margin-top: 20px;
            color: var(--notion-text-primary);
        }

        .faq dd {
            margin-left: 0;
            margin-bottom: 20px;
            color: var(--notion-text-secondary);
            font-size: 0.95em;
            line-height: 1.6;
        }

        footer.cta-section {
            background: var(--notion-bg);
            border-top: 1px solid var(--notion-border);
            padding-top: 50px;
            padding-bottom: 50px;
        }

        footer.cta-section .cta-button {
            margin-top: 15px;
            margin-bottom: 30px;
        }

        footer.cta-section p {
            font-size: 0.9em;
            color: var(--notion-text-secondary);
        }

        footer.cta-section p:last-child {
            margin-top: 30px;
            font-size: 0.85em;
            opacity: 1;
        }

        /* Pricing Section Styles */
        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 40px;
            margin-bottom: 40px;
        }

        .pricing-card {
            background-color: var(--notion-bg);
            border: 1px solid var(--notion-border);
            padding: 25px;
            border-radius: var(--border-radius-subtle);
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .pricing-card>div:first-of-type {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .pricing-card.popular {
            border: 2px solid var(--primary-blue-muted);
            position: relative;
        }

        .pricing-card .popular-badge-container {
            min-height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }

        .pricing-card .popular-badge {
            background-color: var(--primary-blue-muted);
            color: var(--notion-cta-text);
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 600;
            display: inline-block;
            line-height: 1.2;
            margin-bottom: 0;
        }

        .pricing-card .empty-badge-placeholder {
            display: block;
            height: 22px;
        }

        .pricing-card h3 {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.25em;
            font-weight: 600;
            color: var(--notion-text-primary);
            margin-top: 0;
            margin-bottom: 10px;
        }

        .pricing-card .bonus-text {
            color: #28a745;
            font-size: 0.9em;
            font-weight: 500;
            margin-bottom: 10px;
            min-height: 1.35em;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .pricing-card .bonus-text.empty {
            visibility: hidden;
        }

        .pricing-card .price {
            font-family: 'Montserrat', sans-serif;
            font-size: 2em;
            font-weight: 600;
            color: var(--notion-text-primary);
            margin-bottom: 5px;
        }

        .pricing-card .price-per-credit {
            color: var(--notion-text-secondary);
            font-size: 0.85em;
            margin-bottom: 20px;
        }

        .pricing-card .cta-button {

            margin-top: 15px;
        }

        .pricing-card .cta-button.primary {
            background: var(--notion-cta-bg);
            color: var(--notion-cta-text);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25);
        }

        .pricing-card .cta-button.primary:hover {
            background: var(--notion-cta-hover-bg);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.35);
        }

        .pricing-card .cta-button.secondary {
            background-color: var(--notion-accent-subtle);
            color: var(--notion-text-primary);
            border: 1px solid var(--notion-border);
        }

        .pricing-card .cta-button.secondary:hover {
            background-color: var(--notion-border);
        }

        /* Media Queries for Navigation and Hero */
        @media (max-width: 768px) {
            .mobile-nav-toggle-label {
                display: inline-flex;
                align-items: center;
            }

            .nav-links {
                display: none;
            }

            .nav-container {
                justify-content: space-between;
            }

            .site-logo {
                margin-right: 0;
            }

            /* Hero Section Mobile Styles */
            header.hero {
                padding: 40px 0;
                /* Further reduced padding for mobile */
            }

            .hero-container {
                flex-direction: column;
                gap: 20px;
                /* Reduced gap */
                text-align: center;
            }

            .hero-text-content {
                order: 2;
                /* Text below image on mobile */
                width: 100%;
            }

            header.hero h1 {
                font-size: 2em;
                /* Adjusted h1 font size for mobile */
                margin-bottom: 0.4em;
            }

            header.hero h2 {
                font-size: 1em;
                /* Adjusted h2 font size for mobile */
                margin-bottom: 20px;
            }

            .hero-image-placeholder {
                order: 1;
                /* Image above text on mobile */
                width: 100%;
                max-width: 280px;
                /* Constrain image width a bit more */
                height: auto;
                margin: 0 auto 20px auto;
                /* Center and add margin below image */
            }

            .hero-image-placeholder img {
                width: 100%;
                height: auto;
            }

            #mobile-nav-checkbox:checked~.nav-links {
                display: flex !important;
                flex-direction: column;
                position: absolute;
                top: 100%;
                /* Position below the nav bar */
                left: 0;
                right: 0;
                background-color: var(--notion-bg);
                border-top: 1px solid var(--notion-border);
                border-bottom: 1px solid var(--notion-border);
                padding: 10px 0;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                z-index: 1000;
            }

            #mobile-nav-checkbox:checked~.nav-links .main-nav-links {
                flex-direction: column;
                width: 100%;
                margin-right: 0;
            }

            #mobile-nav-checkbox:checked~.nav-links .main-nav-links a,
            #mobile-nav-checkbox:checked~.nav-links .nav-login-link,
            #mobile-nav-checkbox:checked~.nav-links .nav-cta-button {
                width: 100%;
                text-align: center;
                margin-left: 0;
                padding: 12px 20px;
                border-bottom: 1px solid var(--notion-accent-subtle);
                box-sizing: border-box;
            }

            #mobile-nav-checkbox:checked~.nav-links .main-nav-links a:last-of-type {
                border-bottom: 1px solid var(--notion-accent-subtle);
            }

            #mobile-nav-checkbox:checked~.nav-links .nav-login-link {
                border-bottom: 1px solid var(--notion-accent-subtle);
            }

            #mobile-nav-checkbox:checked~.nav-links .nav-cta-button {
                border-bottom: none;
            }
        }

        @media (min-width: 769px) {
            .mobile-nav-toggle-label {
                display: none !important;
            }

            .nav-container {
                display: grid !important;
                grid-template-columns: 1fr auto 1fr !important;
                align-items: center !important;
            }

            .site-logo {
                justify-self: start !important;
            }

            .nav-links {
                display: flex !important;
                flex-direction: row !important;
                position: static !important;
                background-color: transparent !important;
                border-top: none !important;
                border-bottom: none !important;
                padding: 0 !important;
                box-shadow: none !important;
                justify-self: end !important;
            }

            .nav-links .main-nav-links {
                flex-direction: row !important;
                width: auto !important;
                margin-right: 15px !important;
                position: absolute !important;
                left: 50% !important;
                transform: translateX(-50%) !important;
                top: 50% !important;
                transform: translate(-50%, -50%) !important;
            }

            .nav-links .main-nav-links a {
                width: auto !important;
                text-align: center !important;
                margin-left: 24px !important;
                padding: 8px 4px !important;
                border-bottom: none !important;
                font-size: 0.88em !important;
            }

            .nav-links .main-nav-links a:first-child {
                margin-left: 0 !important;
            }

            .nav-links .nav-login-link,
            .nav-links .nav-cta-button {
                width: auto !important;
                text-align: center !important;
                margin-left: 12px !important;
                padding: 8px 12px !important;
                border-bottom: none !important;
                font-size: 0.82em !important;
            }
        }

        /* Responsive for Mac Mockup */
        @media (max-width: 1100px) {
            .mac-mockup {
                width: 800px !important;
                height: 480px !important;
            }

            .mac-screen {
                width: 720px !important;
                height: 440px !important;
                left: 40px !important;
            }
        }

        @media (max-width: 900px) {
            .mac-mockup {
                width: 600px !important;
                height: 360px !important;
            }

            .mac-screen {
                width: 540px !important;
                height: 320px !important;
                left: 30px !important;
            }
        }

        @media (max-width: 700px) {
            .mac-mockup {
                width: 400px !important;
                height: 240px !important;
            }

            .mac-screen {
                width: 360px !important;
                height: 200px !important;
                left: 20px !important;
            }

            .mac-content {
                padding: 20px !important;
            }

            .mac-content h3 {
                font-size: 16px !important;
            }

            .mac-stats {
                grid-template-columns: 1fr !important;
                gap: 10px !important;
            }

            .mac-stat {
                padding: 15px !important;
            }

            .mac-stat-value {
                font-size: 20px !important;
            }

            .mac-covers {
                gap: 8px !important;
            }

            .mac-cover {
                width: 40px !important;
                height: 60px !important;
            }
        }

        /* KDP Descrizione Image Responsive */
        @media (min-width: 1200px) {
            img[src="kdp-descrizione.png"] {
                max-width: 1200px !important;
            }
        }

        @media (max-width: 1199px) and (min-width: 769px) {
            img[src="kdp-descrizione.png"] {
                max-width: 900px !important;
            }
        }

        @media (max-width: 768px) {
            img[src="kdp-descrizione.png"] {
                max-width: 100% !important;
                border-radius: 12px !important;
            }
        }

        /* Enhanced Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 0 16px;
            }

            h1,
            h2 {
                font-size: 2em !important;
            }

            h3 {
                font-size: 1.25em !important;
            }

            .nav-links {
                display: none;
            }

            section {
                padding: 80px 0 !important;
            }

            /* Grid adjustments */
            div[style*="grid-template-columns"] {
                grid-template-columns: 1fr !important;
                gap: 24px !important;
            }

            /* Mockup image responsive */
            img[src="mockup.jpg"] {
                border-radius: 12px !important;
            }
        }

        @media (max-width: 480px) {

            h1,
            h2 {
                font-size: 1.75em !important;
            }

            section {
                padding: 60px 0 !important;
            }

            .site-logo {
                font-size: 1.5em !important;
            }
        }
    </style>
    <script>
        // Get the hash from the URL
        const hash = window.location.hash.substring(1);

        if (hash) {
            // Parse hash parameters
            const params = new URLSearchParams(hash);

            // Check if this is a recovery flow
            if (params.get('type') === 'recovery' && params.get('access_token')) {
                // Redirect to the password update page with query parameters
                const newUrl = '/auth_update_password?' + hash;
                window.location.replace(newUrl);
            } else if (params.get('access_token')) {
                // Regular auth flow - redirect to main app with query parameters
                const newUrl = '/?' + hash;
                window.location.replace(newUrl);
            } else {
                // Show error
                document.getElementById('error-message').style.display = 'block';
                document.getElementById('error-message').textContent = 'Invalid authentication parameters.';
                setTimeout(() => {
                    window.location.replace('/auth_login');
                }, 3000);
            }
        } else {
            // No hash parameters - show error and redirect
            document.getElementById('error-message').style.display = 'block';
            document.getElementById('error-message').textContent = 'No authentication parameters found.';
            setTimeout(() => {
                window.location.replace('/auth_login');
            }, 3000);
        }
    </script>

    <script>
        // Savings Calculator Functionality
        function updateCalculator() {
            const hourlyRate = parseFloat(document.getElementById('hourlyRate').value) || 30;
            const booksPerMonth = parseInt(document.getElementById('booksSlider').value) || 1;

            // Update display values
            document.getElementById('booksCount').textContent = booksPerMonth;
            document.getElementById('displayRate').textContent = hourlyRate;
            document.getElementById('displayBooks').textContent = booksPerMonth;

            // Calculate savings (80 hours per book)
            const hoursPerMonth = 80 * booksPerMonth;
            const monthlySavings = hourlyRate * hoursPerMonth;

            // Update results
            document.getElementById('hoursPerMonth').textContent = hoursPerMonth;
            document.getElementById('monthlySavings').textContent = '€' + monthlySavings.toLocaleString('it-IT');
            document.getElementById('displayTotal').textContent = monthlySavings.toLocaleString('it-IT');
        }

        // Initialize calculator when page loads
        document.addEventListener('DOMContentLoaded', function () {
            const hourlyRateInput = document.getElementById('hourlyRate');
            const booksSlider = document.getElementById('booksSlider');

            if (hourlyRateInput && booksSlider) {
                hourlyRateInput.addEventListener('input', updateCalculator);
                booksSlider.addEventListener('input', updateCalculator);

                // Initial calculation
                updateCalculator();
            }
        });
    </script>
</head>

<body>

    <nav class="site-nav">
        <div class="container nav-container">
            <a href="https://kdpgenius.com" class="site-logo"><img src="kdp.png" alt="KDP GENIUS Logo"></a>

            <input type="checkbox" id="mobile-nav-checkbox">
            <label for="mobile-nav-checkbox" class="mobile-nav-toggle-label">
                <span class="hamburger-icon"></span>
            </label>

            <div class="nav-links">
                <div class="main-nav-links">
                    <a href="#problema">Sfide</a>
                    <a href="#soluzione">Soluzione</a>
                    <a href="#come-funziona">Come Funziona</a>
                    <a href="#usp">Perché Noi</a>
                    <a href="#focus-cover">Cover</a>
                    <a href="#social-proof">Testimonianze</a>
                    <a href="#offerta">Offerta</a>
                    <a href="#faq">FAQ</a>
                </div>
                <a href="https://app.kdpgenius.com/" class=" nav-login-link">Login</a>
                <a href="https://app.kdpgenius.com/auth_signup" class="cta-button nav-cta-button">Get Started</a>
            </div>
        </div>
    </nav>

    <header class="hero">
        <div class="container hero-container">
            <div class="hero-text-content">
                <h1>Domina Amazon KDP. KDP GENIUS: Il Tuo Stratega Editoriale.</h1>
                <h2>Strategia completa per il self-publishing: identità d'autore, nicchie, cover, descrizioni.
                    Costruisci il tuo successo su KDP.</h2>
                <a href="#offerta" class="cta-button hero-cta-button">Inizia Ora la Tua Strategia</a>
            </div>
            <div class="hero-image-placeholder">
                <img src="kdp-hero.png" alt="KDP Genius Abstract Graphic">
            </div>
        </div>
    </header>

    <main>
        <!-- Video Mockup Section -->
        <section style="padding: 120px 0; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
            <div class="container" style="text-align: center;">
                <h2
                    style="font-size: 3em; font-weight: 600; margin-bottom: 30px; color: #1d1d1f; letter-spacing: -0.015em;">
                    Un Percorso Guidato in 12 Step</h2>
                <p
                    style="font-size: 1.375em; color: #64748b; max-width: 700px; margin: 0 auto 60px auto; line-height: 1.4;">
                    Dalla strategia alla pubblicazione, ogni passaggio è ottimizzato per il successo
                </p>

                <!-- Video Container -->
                <div
                    style="max-width: 900px; margin: 0 auto; position: relative; border-radius: 20px; overflow: hidden; box-shadow: 0 25px 50px rgba(0,0,0,0.15);">
                    <div style="position: relative; padding-bottom: 56.25%; height: 0; background: #000;">
                        <!-- Video Placeholder -->
                        <div
                            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center;">
                            <!-- Play Button -->
                            <div
                                style="width: 80px; height: 80px; background: rgba(255,255,255,0.9); border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s ease;">
                                <div
                                    style="width: 0; height: 0; border-left: 25px solid #667eea; border-top: 15px solid transparent; border-bottom: 15px solid transparent; margin-left: 5px;">
                                </div>
                            </div>
                        </div>
                        <!-- Video would go here -->
                        <!-- <iframe src="your-video-url" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none;"></iframe> -->
                    </div>
                </div>

                <p
                    style="font-size: 1.125em; color: #64748b; margin-top: 40px; max-width: 600px; margin-left: auto; margin-right: auto;">
                    Guarda come KDP GENIUS trasforma le tue idee in bestseller Amazon
                </p>
            </div>
        </section>

        <!-- Real Mockup Section -->
        <section>
            <div class="container" style="text-align: center;">
                <h2
                    style="font-size: 3em; font-weight: 600; margin-bottom: 30px; color: #1d1d1f; letter-spacing: -0.015em;">
                    Tutto in un'Unica Piattaforma</h2>
                <p
                    style="font-size: 1.375em; color: #64748b; max-width: 700px; margin: 0 auto 60px auto; line-height: 1.4;">
                    Dashboard professionale per gestire tutti i tuoi progetti editoriali
                </p>

                <!-- Real Mockup Image -->
                <div style="max-width: 1200px; margin: 0 auto;">
                    <img src="kdp-descrizione.png" alt="KDP GENIUS Dashboard"
                        style="width: 100%; height: auto; display: block; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.1);">
                </div>

                <p
                    style="font-size: 1.125em; color: #64748b; margin-top: 40px; max-width: 600px; margin-left: auto; margin-right: auto;">
                    Interfaccia intuitiva progettata per massimizzare la tua produttività
                </p>
            </div>
        </section>
        <section id="problema" style="padding: 120px 0; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
            <div class="container">
                <h2
                    style="font-size: 3em; font-weight: 600; margin-bottom: 60px; color: #1d1d1f; letter-spacing: -0.015em; text-align: center;">
                    Le Sfide del Self-Publisher</h2>
                <div
                    style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 40px; margin-bottom: 60px;">
                    <div
                        style="background: white; padding: 40px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.08); text-align: center; transition: all 0.3s ease;">
                        <div
                            style="width: 80px; height: 80px; background: linear-gradient(135deg, #ff6b6b, #ee5a52); border-radius: 50%; margin: 0 auto 24px auto; display: flex; align-items: center; justify-content: center;">
                            <div style="font-size: 32px;">🎯</div>
                        </div>
                        <h3 style="font-size: 1.5em; font-weight: 600; margin-bottom: 16px; color: #1d1d1f;">Strategia
                            Confusa</h3>
                        <p style="color: #64748b; line-height: 1.6;">Tante idee ma nessuna direzione chiara per
                            costruire un'identità editoriale vincente.</p>
                    </div>
                    <div
                        style="background: white; padding: 40px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.08); text-align: center; transition: all 0.3s ease;">
                        <div
                            style="width: 80px; height: 80px; background: linear-gradient(135deg, #4ecdc4, #44a08d); border-radius: 50%; margin: 0 auto 24px auto; display: flex; align-items: center; justify-content: center;">
                            <div style="font-size: 32px;">🔍</div>
                        </div>
                        <h3 style="font-size: 1.5em; font-weight: 600; margin-bottom: 16px; color: #1d1d1f;">Ricerche
                            Infinite</h3>
                        <p style="color: #64748b; line-height: 1.6;">Ore perse tra keyword e nicchie senza mai trovare
                            quelle giuste per il tuo pubblico.</p>
                    </div>
                    <div
                        style="background: white; padding: 40px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.08); text-align: center; transition: all 0.3s ease;">
                        <div
                            style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; margin: 0 auto 24px auto; display: flex; align-items: center; justify-content: center;">
                            <div style="font-size: 32px;">📚</div>
                        </div>
                        <h3 style="font-size: 1.5em; font-weight: 600; margin-bottom: 16px; color: #1d1d1f;">Vendite
                            Deludenti</h3>
                        <p style="color: #64748b; line-height: 1.6;">Copertine anonime e descrizioni che non convertono.
                            I tuoi libri restano invisibili.</p>
                    </div>
                </div>
                <div style="text-align: center; max-width: 800px; margin: 0 auto;">
                    <p style="font-size: 1.375em; color: #64748b; line-height: 1.5; margin-bottom: 30px;">
                        <strong>E se avessi un sistema che elimina tutte queste frustrazioni?</strong>
                    </p>
                    <p style="font-size: 1.125em; color: #64748b; line-height: 1.6;">
                        Un copilota strategico che ti guida dalla prima idea alla pubblicazione di successo.
                    </p>
                </div>
            </div>
        </section>

        <section id="soluzione" style="padding: 120px 0; background: white;">
            <div class="container" style="text-align:center;">
                <h2
                    style="font-size: 3em; font-weight: 600; margin-bottom: 30px; color: #1d1d1f; letter-spacing: -0.015em;">
                    Ecco KDP GENIUS</h2>
                <h3
                    style="font-size: 1.5em; font-weight: 400; color: #64748b; margin-bottom: 60px; max-width: 800px; margin-left: auto; margin-right: auto; line-height: 1.4;">
                    Il primo sistema strategico completo che trasforma le tue idee in bestseller Amazon
                </h3>

                <!-- Features Grid -->
                <div
                    style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 40px; margin-top: 80px;">
                    <div style="text-align: center;">
                        <div
                            style="width: 100px; height: 100px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 20px; margin: 0 auto 24px auto; display: flex; align-items: center; justify-content: center;">
                            <div style="font-size: 40px; color: white;">🎯</div>
                        </div>
                        <h4 style="font-size: 1.375em; font-weight: 600; margin-bottom: 16px; color: #1d1d1f;">Strategia
                            Completa</h4>
                        <p style="color: #64748b; line-height: 1.6;">Dalla vision alla pubblicazione, ogni step
                            ottimizzato per il successo</p>
                    </div>
                    <div style="text-align: center;">
                        <div
                            style="width: 100px; height: 100px; background: linear-gradient(135deg, #4ecdc4, #44a08d); border-radius: 20px; margin: 0 auto 24px auto; display: flex; align-items: center; justify-content: center;">
                            <div style="font-size: 40px; color: white;">⚡</div>
                        </div>
                        <h4 style="font-size: 1.375em; font-weight: 600; margin-bottom: 16px; color: #1d1d1f;">Risultati
                            Immediati</h4>
                        <p style="color: #64748b; line-height: 1.6;">Output pronti all'uso: copertine, descrizioni,
                            titoli perfetti</p>
                    </div>
                    <div style="text-align: center;">
                        <div
                            style="width: 100px; height: 100px; background: linear-gradient(135deg, #ff6b6b, #ee5a52); border-radius: 20px; margin: 0 auto 24px auto; display: flex; align-items: center; justify-content: center;">
                            <div style="font-size: 40px; color: white;">🚀</div>
                        </div>
                        <h4 style="font-size: 1.375em; font-weight: 600; margin-bottom: 16px; color: #1d1d1f;">Zero
                            Rischi</h4>
                        <p style="color: #64748b; line-height: 1.6;">Controlli automatici per evitare violazioni e
                            errori costosi</p>
                    </div>
                </div>
            </div>
        </section>



        <section id="usp" style="padding: 120px 0; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
            <div class="container">
                <h2
                    style="font-size: 3em; font-weight: 600; margin-bottom: 30px; color: #1d1d1f; letter-spacing: -0.015em; text-align: center;">
                    Perché KDP GENIUS è Diverso
                </h2>
                <p
                    style="text-align: center; max-width: 700px; margin: 0 auto 60px auto; font-size: 1.375em; color: #64748b; line-height: 1.4;">
                    Non solo contenuti, ma strategia editoriale completa. Costruisci qualcosa di tuo.
                </p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 40px;">
                    <div
                        style="background: white; padding: 32px; border-radius: 16px; box-shadow: 0 8px 25px rgba(0,0,0,0.06);">
                        <div style="font-size: 2.5em; margin-bottom: 20px;">🏗️</div>
                        <h4 style="font-size: 1.25em; font-weight: 600; margin-bottom: 12px; color: #1d1d1f;">Brand
                            d'Autore</h4>
                        <p style="color: #64748b; line-height: 1.6;">Identità editoriale forte e coerente, non solo
                            libri singoli</p>
                    </div>
                    <div
                        style="background: white; padding: 32px; border-radius: 16px; box-shadow: 0 8px 25px rgba(0,0,0,0.06);">
                        <div style="font-size: 2.5em; margin-bottom: 20px;">📊</div>
                        <h4 style="font-size: 1.25em; font-weight: 600; margin-bottom: 12px; color: #1d1d1f;">Dati Reali
                        </h4>
                        <p style="color: #64748b; line-height: 1.6;">Decisioni basate su ricerche concrete, zero
                            congetture</p>
                    </div>
                    <div
                        style="background: white; padding: 32px; border-radius: 16px; box-shadow: 0 8px 25px rgba(0,0,0,0.06);">
                        <div style="font-size: 2.5em; margin-bottom: 20px;">🎯</div>
                        <h4 style="font-size: 1.25em; font-weight: 600; margin-bottom: 12px; color: #1d1d1f;">Tutto
                            Ottimizzato</h4>
                        <p style="color: #64748b; line-height: 1.6;">Ogni elemento perfetto per la vendita su Amazon</p>
                    </div>
                    <div
                        style="background: white; padding: 32px; border-radius: 16px; box-shadow: 0 8px 25px rgba(0,0,0,0.06);">
                        <div style="font-size: 2.5em; margin-bottom: 20px;">⚡</div>
                        <h4 style="font-size: 1.25em; font-weight: 600; margin-bottom: 12px; color: #1d1d1f;">Tempo
                            Risparmiato</h4>
                        <p style="color: #64748b; line-height: 1.6;">Processi automatizzati, più tempo per scrivere</p>
                    </div>
                    <div
                        style="background: white; padding: 32px; border-radius: 16px; box-shadow: 0 8px 25px rgba(0,0,0,0.06);">
                        <div style="font-size: 2.5em; margin-bottom: 20px;">🎮</div>
                        <h4 style="font-size: 1.25em; font-weight: 600; margin-bottom: 12px; color: #1d1d1f;">Controllo
                            Totale</h4>
                        <p style="color: #64748b; line-height: 1.6;">Output chiari e pronti, tu decidi sempre</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="focus-cover" style="padding: 120px 0; background: #000000; color: #ffffff;">
            <div class="container" style="text-align: center;">
                <h2
                    style="font-size: 3em; font-weight: 600; margin-bottom: 30px; color: #ffffff; letter-spacing: -0.015em;">
                    Cover che Vendono</h2>
                <p
                    style="font-size: 1.375em; color: #a1a1a6; max-width: 700px; margin: 0 auto 80px auto; line-height: 1.4;">
                    Design professionali generati automaticamente. Nessuna competenza grafica richiesta.
                </p>

                <!-- Ebook Covers Showcase - Apple Style -->
                <div
                    style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 30px; max-width: 900px; margin: 0 auto 60px auto; padding: 0 20px;">
                    <!-- Cover 1 -->
                    <div style="position: relative; transform: rotate(-2deg); transition: transform 0.3s ease;">
                        <img src="cover1.jpeg" alt="KDP Book Cover 1"
                            style="width: 100%; height: auto; border-radius: 12px; box-shadow: 0 25px 50px rgba(0,0,0,0.4); transition: all 0.3s ease;"
                            onmouseover="this.style.transform='scale(1.05) rotate(0deg)'; this.parentElement.style.transform='rotate(0deg) scale(1.05)';"
                            onmouseout="this.style.transform='scale(1) rotate(0deg)'; this.parentElement.style.transform='rotate(-2deg) scale(1)';">
                    </div>

                    <!-- Cover 2 -->
                    <div style="position: relative; transform: rotate(1deg); transition: transform 0.3s ease;">
                        <img src="cover2.jpeg" alt="KDP Book Cover 2"
                            style="width: 100%; height: auto; border-radius: 12px; box-shadow: 0 25px 50px rgba(0,0,0,0.4); transition: all 0.3s ease;"
                            onmouseover="this.style.transform='scale(1.05) rotate(0deg)'; this.parentElement.style.transform='rotate(0deg) scale(1.05)';"
                            onmouseout="this.style.transform='scale(1) rotate(0deg)'; this.parentElement.style.transform='rotate(1deg) scale(1)';">
                    </div>

                    <!-- Cover 3 -->
                    <div style="position: relative; transform: rotate(-1deg); transition: transform 0.3s ease;">
                        <img src="cover3.jpeg" alt="KDP Book Cover 3"
                            style="width: 100%; height: auto; border-radius: 12px; box-shadow: 0 25px 50px rgba(0,0,0,0.4); transition: all 0.3s ease;"
                            onmouseover="this.style.transform='scale(1.05) rotate(0deg)'; this.parentElement.style.transform='rotate(0deg) scale(1.05)';"
                            onmouseout="this.style.transform='scale(1) rotate(0deg)'; this.parentElement.style.transform='rotate(-1deg) scale(1)';">
                    </div>

                    <!-- Cover 4 -->
                    <div style="position: relative; transform: rotate(2deg); transition: transform 0.3s ease;">
                        <img src="cover4.jpeg" alt="KDP Book Cover 4"
                            style="width: 100%; height: auto; border-radius: 12px; box-shadow: 0 25px 50px rgba(0,0,0,0.4); transition: all 0.3s ease;"
                            onmouseover="this.style.transform='scale(1.05) rotate(0deg)'; this.parentElement.style.transform='rotate(0deg) scale(1.05)';"
                            onmouseout="this.style.transform='scale(1) rotate(0deg)'; this.parentElement.style.transform='rotate(2deg) scale(1)';">
                    </div>
                </div>

                <p style="font-size: 1.125em; color: #a1a1a6; max-width: 600px; margin: 0 auto;">
                    4 proposte professionali generate automaticamente per ogni libro. Scegli, personalizza e pubblica.
                </p>
            </div>
        </section>

        <section id="social-proof"
            style="padding: 120px 0; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
            <div class="container">
                <h2
                    style="font-size: 3em; font-weight: 600; margin-bottom: 60px; color: #1d1d1f; letter-spacing: -0.015em; text-align: center;">
                    Cosa Dicono gli Autori che Usano KDP GENIUS</h2>

                <div
                    style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 40px; margin-top: 60px;">
                    <!-- Testimonial 1 -->
                    <div
                        style="background: white; padding: 40px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.08); text-align: center; position: relative;">
                        <div
                            style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #667eea, #764ba2); margin: 0 auto 24px auto; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: 600;">
                            MR
                        </div>
                        <h4 style="font-size: 1.25em; font-weight: 600; margin-bottom: 8px; color: #1d1d1f;">Marco Rossi
                        </h4>
                        <p style="font-size: 0.9em; color: #64748b; margin-bottom: 20px; font-style: italic;">Autore di
                            Saggistica</p>
                        <p style="color: #1d1d1f; line-height: 1.6; font-size: 1em;">
                            "Ho sempre faticato a trovare le keyword giuste, ma con KDP GENIUS ho scoperto nicchie che
                            non avrei mai immaginato! Le mie vendite sono aumentate del 300% in soli 3 mesi."
                        </p>
                        <div style="position: absolute; top: 20px; right: 20px; color: #fbbf24; font-size: 24px;">★★★★★
                        </div>
                    </div>

                    <!-- Testimonial 2 -->
                    <div
                        style="background: white; padding: 40px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.08); text-align: center; position: relative;">
                        <div
                            style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #f093fb, #f5576c); margin: 0 auto 24px auto; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: 600;">
                            LB
                        </div>
                        <h4 style="font-size: 1.25em; font-weight: 600; margin-bottom: 8px; color: #1d1d1f;">Laura
                            Bianchi</h4>
                        <p style="font-size: 0.9em; color: #64748b; margin-bottom: 20px; font-style: italic;">Autrice di
                            Self-Help</p>
                        <p style="color: #1d1d1f; line-height: 1.6; font-size: 1em;">
                            "La funzione Cover Perfetta è geniale! Ho risparmiato centinaia di euro in grafici e
                            finalmente ho copertine che vendono. Il ROI è stato immediato."
                        </p>
                        <div style="position: absolute; top: 20px; right: 20px; color: #fbbf24; font-size: 24px;">★★★★★
                        </div>
                    </div>

                    <!-- Testimonial 3 -->
                    <div
                        style="background: white; padding: 40px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.08); text-align: center; position: relative;">
                        <div
                            style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #4facfe, #00f2fe); margin: 0 auto 24px auto; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: 600;">
                            AV
                        </div>
                        <h4 style="font-size: 1.25em; font-weight: 600; margin-bottom: 8px; color: #1d1d1f;">Andrea
                            Verdi</h4>
                        <p style="font-size: 0.9em; color: #64748b; margin-bottom: 20px; font-style: italic;">Autore di
                            Business</p>
                        <p style="color: #1d1d1f; line-height: 1.6; font-size: 1em;">
                            "KDP GENIUS mi ha dato la struttura e la strategia che mi mancavano. Ora mi sento un vero
                            imprenditore editoriale e non più solo uno scrittore."
                        </p>
                        <div style="position: absolute; top: 20px; right: 20px; color: #fbbf24; font-size: 24px;">★★★★★
                        </div>
                    </div>

                    <!-- Testimonial 4 -->
                    <div
                        style="background: white; padding: 40px; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.08); text-align: center; position: relative;">
                        <div
                            style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #ff6b6b, #ee5a52); margin: 0 auto 24px auto; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: 600;">
                            SF
                        </div>
                        <h4 style="font-size: 1.25em; font-weight: 600; margin-bottom: 8px; color: #1d1d1f;">Sofia
                            Ferrari</h4>
                        <p style="font-size: 0.9em; color: #64748b; margin-bottom: 20px; font-style: italic;">Autrice di
                            Romance</p>
                        <p style="color: #1d1d1f; line-height: 1.6; font-size: 1em;">
                            "Incredibile come KDP GENIUS abbia trasformato il mio approccio al self-publishing. Ora
                            pubblico con sicurezza e i risultati parlano chiaro: +250% di vendite!"
                        </p>
                        <div style="position: absolute; top: 20px; right: 20px; color: #fbbf24; font-size: 24px;">★★★★★
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Savings Calculator Section -->
        <section id="calculator"
            style="padding: 120px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="container" style="text-align: center;">
                <h2
                    style="font-size: 3em; font-weight: 600; margin-bottom: 30px; color: #ffffff; letter-spacing: -0.015em;">
                    Calcola il Tuo Risparmio
                </h2>
                <p
                    style="font-size: 1.375em; color: rgba(255,255,255,0.8); max-width: 700px; margin: 0 auto 60px auto; line-height: 1.4;">
                    Scopri quanto tempo e denaro risparmi ogni mese con KDP GENIUS
                </p>

                <div
                    style="max-width: 600px; margin: 0 auto; background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 20px; padding: 40px; box-shadow: 0 20px 40px rgba(0,0,0,0.1);">
                    <!-- Hourly Rate Input -->
                    <div style="margin-bottom: 40px;">
                        <label
                            style="display: block; font-size: 1.125em; font-weight: 500; margin-bottom: 15px; color: white;">
                            Il tuo costo orario (€)
                        </label>
                        <input type="number" id="hourlyRate" value="30" min="10" max="200"
                            style="width: 100%; padding: 16px; font-size: 1.25em; text-align: center; border: none; border-radius: 12px; background: rgba(255,255,255,0.9); color: #1d1d1f; font-weight: 600;">
                    </div>

                    <!-- Books per Month Slider -->
                    <div style="margin-bottom: 40px;">
                        <label
                            style="display: block; font-size: 1.125em; font-weight: 500; margin-bottom: 15px; color: white;">
                            Libri che scrivi al mese: <span id="booksCount" style="font-weight: 700;">1</span>
                        </label>
                        <input type="range" id="booksSlider" min="1" max="10" value="1"
                            style="width: 100%; height: 8px; border-radius: 4px; background: rgba(255,255,255,0.3); outline: none; -webkit-appearance: none; appearance: none;">
                        <style>
                            #booksSlider::-webkit-slider-thumb {
                                -webkit-appearance: none;
                                appearance: none;
                                width: 24px;
                                height: 24px;
                                border-radius: 50%;
                                background: #ffffff;
                                cursor: pointer;
                                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                            }

                            #booksSlider::-moz-range-thumb {
                                width: 24px;
                                height: 24px;
                                border-radius: 50%;
                                background: #ffffff;
                                cursor: pointer;
                                border: none;
                                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                            }
                        </style>
                    </div>

                    <!-- Results -->
                    <div
                        style="background: rgba(255,255,255,0.15); border-radius: 16px; padding: 30px; margin-bottom: 30px;">
                        <div style="font-size: 1.125em; color: rgba(255,255,255,0.8); margin-bottom: 10px;">
                            Risparmio mensile
                        </div>
                        <div id="monthlySavings"
                            style="font-size: 3.5em; font-weight: 700; color: #ffffff; margin-bottom: 10px;">
                            €2,400
                        </div>
                        <div style="font-size: 1em; color: rgba(255,255,255,0.7);">
                            <span id="hoursPerMonth">80</span> ore risparmiate al mese
                        </div>
                    </div>

                    <div style="font-size: 1em; color: rgba(255,255,255,0.8); line-height: 1.5;">
                        <strong>Formula:</strong> 80 ore × €<span id="displayRate">30</span> × <span
                            id="displayBooks">1</span> libro/mese = €<span id="displayTotal">2,400</span>
                    </div>
                </div>
            </div>
        </section>

        <section id="offerta" class="cta-section">
            <div class="container">
                <h2 class="section-title">Pronto a Trasformare la Tua Carriera di Autore Self-Publisher?</h2>
                <p>Scegli il piano KDP GENIUS più adatto a te e inizia subito a costruire il tuo successo editoriale.
                </p>

                <div class="pricing-grid">
                    <!-- Card 1: 50 Credits -->
                    <div class="pricing-card">
                        <div>
                            <div class="popular-badge-container">
                                <span class="empty-badge-placeholder"></span> <!-- Placeholder for alignment -->
                            </div>
                            <h3>50 Credits</h3>
                            <p class="bonus-text empty">&nbsp;</p>
                            <h2 class="price">€17.99</h2>
                            <p class="price-per-credit">€0.360 per credit</p>
                        </div>
                        <a href="https://app.kdpgenius.com/auth_signup" class="cta-button secondary">Compra 50
                            Crediti</a>
                    </div>

                    <!-- Card 2: 100 Credits (Popular) -->
                    <div class="pricing-card popular">
                        <div>
                            <div class="popular-badge-container">
                                <span class="popular-badge">POPULAR</span>
                            </div>
                            <h3>100 Credits</h3>
                            <p class="bonus-text">+50 Bonus Credits!</p>
                            <h2 class="price">€39.99</h2>
                            <p class="price-per-credit">€0.267 per credit</p>
                        </div>
                        <a href="https://app.kdpgenius.com/auth_signup" class="cta-button primary">Compra 150
                            Crediti</a>
                    </div>

                    <!-- Card 3: 200 Credits -->
                    <div class="pricing-card">
                        <div>
                            <div class="popular-badge-container">
                                <span class="empty-badge-placeholder"></span> <!-- Placeholder for alignment -->
                            </div>
                            <h3>200 Credits</h3>
                            <p class="bonus-text">+100 Bonus Credits!</p>
                            <h2 class="price">€69.99</h2>
                            <p class="price-per-credit">€0.233 per credit</p>
                        </div>
                        <a href="https://app.kdpgenius.com/auth_signup" class="cta-button secondary">Compra 300
                            Crediti</a>
                    </div>
                </div>

                <a href="https://app.kdpgenius.com/auth_signup" class="cta-button" style="margin-top: 30px;">Sì, Voglio
                    Costruire la Mia Identità Editoriale e Vendere Più Libri!</a>
                <div class="placeholder"
                    style="margin-top: 25px; background-color: var(--notion-bg); border: 1px dashed var(--notion-border);">
                    <p><strong>[Placeholder Garanzia - SE APPLICABILE]</strong><br>
                        Prova KDP GENIUS Senza Rischi! Soddisfatto o Rimborsato per X Giorni</p>
                </div>
            </div>
        </section>

        <section id="faq">
            <div class="container">
                <h2 class="section-title">Domande Frequenti (FAQ)</h2>
                <dl class="faq">
                    <dt>D: KDP GENIUS scrive i libri al posto mio?</dt>
                    <dd>R: No, KDP GENIUS è uno strumento strategico. Non si limita a generare contenuti, ma ti guida
                        nel costruire una solida identità editoriale e nel prendere decisioni informate su cosa scrivere
                        e come posizionarlo sul mercato. Ti aiuta a creare la struttura, i metadati e i materiali di
                        marketing, ma la creatività della scrittura resta tua.</dd>

                    <dt>D: Cosa ottengo concretamente con l'Esportazione Finale?</dt>
                    <dd>R: Riceverai documenti in formato PDF e Word contenenti: il profilo dettagliato della tua buyer
                        persona, l'indice strutturato del tuo libro, proposte di titoli e sottotitoli, la descrizione
                        Amazon persuasiva, la tua bio autore e la copertina scelta. Tutto pronto per la pubblicazione o
                        per ulteriori lavorazioni.</dd>

                    <dt>D: È difficile da usare? Serve essere esperti di tecnologia?</dt>
                    <dd>R: KDP GENIUS è progettato per essere intuitivo e guidarti passo dopo passo. L'obiettivo è
                        semplificare la complessità, non aggiungerla.</dd>

                    <dt>D: Per quali generi di libri è adatto KDP GENIUS?</dt>
                    <dd>R: KDP GENIUS è versatile e può essere utilizzato da autori di saggistica, manualistica,
                        self-help, e anche per la pianificazione strategica di romanzi (identità autore, target,
                        posizionamento). Alcuni tool, come quello per l'indice, possono avere suggerimenti più specifici
                        per la non-fiction.</dd>
                </dl>
            </div>
        </section>

    </main>

    <footer
        style="background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); color: white; padding: 80px 0 40px 0; position: relative; overflow: hidden;">
        <!-- Background Pattern -->
        <div
            style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.05; background-image: radial-gradient(circle at 25% 25%, #ffffff 2px, transparent 2px), radial-gradient(circle at 75% 75%, #ffffff 2px, transparent 2px); background-size: 50px 50px;">
        </div>

        <div class="container" style="position: relative; z-index: 1; text-align: center;">
            <!-- Main CTA Section -->
            <div style="max-width: 800px; margin: 0 auto 60px auto;">
                <h2
                    style="font-size: 2.5em; font-weight: 600; margin-bottom: 24px; color: #ffffff; letter-spacing: -0.015em; line-height: 1.2;">
                    Non lasciare che le tue idee restino nel cassetto.
                </h2>
                <p style="font-size: 1.25em; color: #a1a1a6; line-height: 1.6; margin-bottom: 40px;">
                    È il momento di passare da "scrittore" a "imprenditore editoriale di successo".<br>
                    <strong style="color: #ffffff;">KDP GENIUS è lo stratega che ti serve.</strong>
                </p>

                <a href="https://app.kdpgenius.com/auth_signup"
                    style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 40px; font-size: 1.125em; font-weight: 600; text-decoration: none; border-radius: 16px; transition: all 0.3s ease; box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3); text-align: center; border: none; letter-spacing: -0.01em;"
                    onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 15px 40px rgba(102, 126, 234, 0.4)';"
                    onmouseout="this.style.transform='translateY(0px)'; this.style.boxShadow='0 10px 30px rgba(102, 126, 234, 0.3)';">
                    🚀 Accedi Subito a KDP GENIUS e Inizia a Costruire il Tuo Futuro!
                </a>
            </div>

            <!-- Divider -->
            <div
                style="height: 1px; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); margin: 60px 0 40px 0;">
            </div>

            <!-- Footer Bottom -->
            <div
                style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 20px;">

                <div style="font-size: 0.9em; color: #a1a1a6;">
                    &copy; 2025 KDP GENIUS - Tutti i diritti riservati.
                </div>
            </div>
        </div>
    </footer>

</body>

</html>